import matplotlib.pyplot as plt
import numpy as np

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def plot_put_option_promotional():
    """
    绘制欧式香草看跌期权宣传图 - 精细化版本
    红色期权曲线，黑色类坐标轴线和箭头，无数值显示
    """
    fig, ax = plt.subplots(figsize=(10, 6))

    # 生成标准化的价格范围
    x = np.linspace(0.2, 1.8, 1000)

    # 看跌期权收益曲线参数
    strike_point = 1.0  # 行权价格点
    initial_point = 1.0  # 期初价格点
    fixed_return = 0.1  # 固定收益率水平

    # 计算看跌期权收益曲线
    payoff = np.where(x < strike_point,
                     fixed_return + (strike_point - x) * 1,  # 看跌期权收益部分
                     fixed_return)  # 固定收益率部分

    # 绘制期权收益曲线 - 红色
    ax.plot(x, payoff, color='red', linewidth=3)

    # 完全隐藏所有默认坐标轴
    ax.spines['top'].set_visible(False)
    ax.spines['right'].set_visible(False)
    ax.spines['left'].set_visible(False)
    ax.spines['bottom'].set_visible(False)
    ax.set_xticks([])
    ax.set_yticks([])

    # 设置坐标轴范围
    ax.set_xlim(0, 2)
    ax.set_ylim(0, 1.2)

    # 绘制类似坐标轴的线和箭头 - 黑色
    ax.annotate('', xy=(1.9, 0.1), xytext=(0.1, 0.1),
                arrowprops=dict(arrowstyle='->', color='black', lw=2))
    ax.plot([0.1, 1.85], [0.1, 0.1], color='black', linewidth=2)
    ax.annotate('', xy=(0.1, 1.1), xytext=(0.1, 0.1),
                arrowprops=dict(arrowstyle='->', color='black', lw=2))
    ax.plot([0.1, 0.1], [0.1, 1.05], color='black', linewidth=2)
    ax.text(1.95, 0.05, '标的价格', fontsize=12, fontweight='bold',
            ha='right', va='top', color='black')
    ax.text(0.05, 1.15, '损益', fontsize=12, fontweight='bold',
            ha='right', va='bottom', color='black', rotation=90)

    ax.plot([initial_point, initial_point], [0.1, 0.12],
            color='black', linestyle='--', linewidth=1.5, alpha=0.7)
    ax.text(initial_point, 0.05, '行权价', fontsize=10, fontweight='bold',
            ha='center', va='top', color='black')

    ax.plot([strike_point + 0.1, strike_point + 0.1], [0.1, 0.12],
            color='black', linestyle='--', linewidth=1.5, alpha=0.7)
    ax.text(strike_point + 0.1, 0.05, '期初价', fontsize=10, fontweight='bold',
            ha='center', va='top', color='black')

    # 设置背景色为纯白
    fig.patch.set_facecolor('white')
    ax.set_facecolor('white')
    ax.set_title('')
    ax.set_xlabel('')
    ax.set_ylabel('')
    plt.tight_layout()
    return fig, ax

if __name__ == "__main__":
    fig, ax = plot_put_option_promotional()
    plt.savefig('Put_1.png', dpi=300, bbox_inches='tight', facecolor='white')
    plt.show()
    print("欧式香草看跌期权宣传图已生成完成！")