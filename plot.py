import matplotlib.pyplot as plt
import numpy as np

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def calculate_put_option_payoff(S_T, S_0, K, participation_rate, fixed_rate, principal=1):
    """
    计算欧式香草看跌期权收益

    参数:
    S_T: 期末价格数组
    S_0: 期初价格
    K: 行权价格
    participation_rate: 参与率 (%)
    fixed_rate: 固定收益率 (%)
    principal: 名义本金额

    返回:
    收益数组
    """
    # 看跌期权收益 = Max(行权价格-期末价格, 0)
    option_payoff = np.maximum(K - S_T, 0)

    # 总收益 = 名义本金 × (期权收益/期初价格 × 参与率 + 固定收益率)
    total_payoff = principal * (option_payoff / S_0 * participation_rate / 100 + fixed_rate / 100)

    return total_payoff

def plot_put_option_payoff():
    """
    绘制欧式香草看跌期权收益图
    """
    # 参数设置
    S_0 = 100  # 期初价格
    K = 100    # 行权价格 (等于期初价格)
    participation_rate = 100  # 参与率 100%
    fixed_rate = 3  # 固定收益率 3%
    principal = 1  # 名义本金额 (标准化为1)

    # 生成期末价格范围 (从期初价格的50%到150%)
    S_T = np.linspace(S_0 * 0.5, S_0 * 1.5, 1000)

    # 计算收益
    payoff = calculate_put_option_payoff(S_T, S_0, K, participation_rate, fixed_rate, principal)

    # 创建图形
    fig, ax = plt.subplots(figsize=(12, 8))

    # 绘制收益曲线
    ax.plot(S_T, payoff, 'b-', linewidth=2.5, label='看跌期权收益')

    # 绘制关键点
    # 行权价格处的垂直线
    ax.axvline(x=K, color='red', linestyle='--', alpha=0.7, label=f'行权价格 = {K}')

    # 固定收益率水平线
    ax.axhline(y=fixed_rate/100, color='green', linestyle='--', alpha=0.7, label=f'固定收益率 = {fixed_rate}%')

    # 期初价格处的垂直线
    ax.axvline(x=S_0, color='orange', linestyle='--', alpha=0.7, label=f'期初价格 = {S_0}')

    # 设置坐标轴
    ax.set_xlabel('期末价格 (S_T)', fontsize=12, fontweight='bold')
    ax.set_ylabel('收益率', fontsize=12, fontweight='bold')
    ax.set_title('欧式香草看跌期权收益图', fontsize=16, fontweight='bold', pad=20)

    # 设置网格
    ax.grid(True, alpha=0.3)

    # 设置图例
    ax.legend(fontsize=10, loc='upper right')

    # 添加文本说明
    ax.text(0.02, 0.98, f'参数设置:\n期初价格: {S_0}\n行权价格: {K}\n参与率: {participation_rate}%\n固定收益率: {fixed_rate}%',
            transform=ax.transAxes, fontsize=10, verticalalignment='top',
            bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

    # 格式化y轴为百分比
    ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda y, _: '{:.1%}'.format(y)))

    # 调整布局
    plt.tight_layout()

    return fig, ax

def plot_put_option_comparison():
    """
    绘制不同参数下的看跌期权收益对比图
    """
    # 基础参数
    S_0 = 100
    K = 100
    fixed_rate = 3
    principal = 1

    # 期末价格范围
    S_T = np.linspace(S_0 * 0.5, S_0 * 1.5, 1000)

    # 不同参与率
    participation_rates = [50, 100, 150]

    # 创建图形
    fig, ax = plt.subplots(figsize=(12, 8))

    colors = ['blue', 'red', 'green']

    for i, rate in enumerate(participation_rates):
        payoff = calculate_put_option_payoff(S_T, S_0, K, rate, fixed_rate, principal)
        ax.plot(S_T, payoff, color=colors[i], linewidth=2.5,
                label=f'参与率 = {rate}%')

    # 绘制关键线
    ax.axvline(x=K, color='black', linestyle='--', alpha=0.7, label=f'行权价格 = {K}')
    ax.axhline(y=fixed_rate/100, color='gray', linestyle='--', alpha=0.7, label=f'固定收益率 = {fixed_rate}%')

    # 设置坐标轴
    ax.set_xlabel('期末价格 (S_T)', fontsize=12, fontweight='bold')
    ax.set_ylabel('收益率', fontsize=12, fontweight='bold')
    ax.set_title('不同参与率下的欧式香草看跌期权收益对比', fontsize=16, fontweight='bold', pad=20)

    # 设置网格和图例
    ax.grid(True, alpha=0.3)
    ax.legend(fontsize=10, loc='upper right')

    # 格式化y轴为百分比
    ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda y, _: '{:.1%}'.format(y)))

    plt.tight_layout()

    return fig, ax

if __name__ == "__main__":
    # 绘制基础看跌期权收益图
    fig1, ax1 = plot_put_option_payoff()
    plt.savefig('Put_Option_Payoff.png', dpi=300, bbox_inches='tight')
    plt.show()

    # 绘制不同参数对比图
    fig2, ax2 = plot_put_option_comparison()
    plt.savefig('Put_Option_Comparison.png', dpi=300, bbox_inches='tight')
    plt.show()

    print("欧式香草看跌期权收益图已生成完成！")
    print("生成的文件:")
    print("- Put_Option_Payoff.png: 基础看跌期权收益图")
    print("- Put_Option_Comparison.png: 不同参与率对比图")