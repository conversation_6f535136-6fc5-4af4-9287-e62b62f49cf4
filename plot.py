import matplotlib.pyplot as plt
import numpy as np

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def plot_put_option_promotional():
    """
    绘制欧式香草看跌期权宣传图 - 简洁版本，无数值标注
    """
    # 生成标准化的价格范围
    x = np.linspace(0, 2, 1000)

    # 看跌期权收益曲线 (简化的理想形状)
    # 当价格低于行权价时，收益随价格下降而增加
    # 当价格高于行权价时，收益保持在固定收益率水平
    strike_point = 1.0  # 标准化行权价格点
    fixed_return = 0.2  # 固定收益率水平

    # 计算收益曲线
    payoff = np.where(x < strike_point,
                     fixed_return + (strike_point - x) * 0.8,  # 看跌期权收益部分
                     fixed_return)  # 固定收益率部分

    # 创建图形
    fig, ax = plt.subplots(figsize=(10, 6))

    # 绘制主要收益曲线 - 使用渐变色
    ax.plot(x, payoff, color='#2E86AB', linewidth=4, alpha=0.9)

    # 填充收益区域
    ax.fill_between(x, 0, payoff, alpha=0.2, color='#2E86AB')

    # 绘制关键分界线 (不显示数值)
    ax.axvline(x=strike_point, color='#F24236', linestyle='--', linewidth=2, alpha=0.7)
    ax.axhline(y=fixed_return, color='#A23B72', linestyle='--', linewidth=2, alpha=0.7)

    # 设置坐标轴标签 (不显示具体数值)
    ax.set_xlabel('标的资产价格', fontsize=14, fontweight='bold', color='#333333')
    ax.set_ylabel('投资收益', fontsize=14, fontweight='bold', color='#333333')
    ax.set_title('欧式香草看跌期权收益结构', fontsize=18, fontweight='bold',
                color='#2E86AB', pad=25)

    # 隐藏坐标轴刻度和数值
    ax.set_xticks([])
    ax.set_yticks([])

    # 设置坐标轴样式
    ax.spines['top'].set_visible(False)
    ax.spines['right'].set_visible(False)
    ax.spines['left'].set_color('#CCCCCC')
    ax.spines['bottom'].set_color('#CCCCCC')
    ax.spines['left'].set_linewidth(1.5)
    ax.spines['bottom'].set_linewidth(1.5)

    # 设置背景色
    fig.patch.set_facecolor('white')
    ax.set_facecolor('#FAFAFA')

    # 调整布局
    plt.tight_layout()

    return fig, ax

if __name__ == "__main__":
    # 绘制宣传版看跌期权收益图
    fig, ax = plot_put_option_promotional()
    plt.savefig('Put_1.png', dpi=300, bbox_inches='tight', facecolor='white')
    plt.show()

    print("欧式香草看跌期权宣传图已生成完成！")
    print("生成的文件: Put_1.png")